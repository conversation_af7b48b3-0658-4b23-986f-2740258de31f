from django.contrib import admin
from django.urls import path, include, re_path
from django.conf import settings
from django.conf.urls.static import static

# Swagger imports
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi

# Create schema view for Swagger documentation
schema_view = get_schema_view(
    openapi.Info(
        title="Online Application Portal API",
        default_version='v1',
        description="API documentation for the Online Application Portal",
        terms_of_service="https://www.example.com/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="BSD License"),
    ),
    public=True,
    permission_classes=(permissions.AllowAny,),
)

urlpatterns = [
    # Admin URLs
    path('admin/', admin.site.urls),

    # Swagger URLs
    re_path(r'^swagger(?P<format>\.json|\.yaml)$', schema_view.without_ui(cache_timeout=0), name='schema-json'),
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),

    # API URLs
    path('api/', include('setups.college.urls')),
    path('api/', include('setups.department.urls')),
    path('api/', include('setups.admission_type.urls')),
    path('api/', include('setups.application_information.urls')),
    path('api/', include('setups.program.urls')),
    path('api/', include('setups.registration_period.urls')),
    path('api/', include('setups.study_field.urls')),
    path('api/', include('setups.study_program.urls')),
    path('api/', include('setups.year.urls')),
    path('api/', include('setups.term.urls')),
    path('api/', include('setups.certificate_type.urls')),
    path('api/', include('setups.document_type.urls')),
    path('api/', include('setups.service_type.urls')),
    path('api/', include('official.urls')),
    path('api/', include('registration.urls')),
    path('api/', include('GraduateVerification.urls')),
    path('api/admin/', include('admin_utils.urls')),

    path('api/settings/', include('settings_manager.urls')),
    path('api/', include('services.urls')),
    path('api/', include('service_requests.urls')),
    path('api/communication/', include('communication.urls')),
    path('api/', include('alumni_applications.urls')),
    path('api/', include('downloadable.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)



