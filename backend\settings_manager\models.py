from django.db import models
from django.core.validators import RegexValidator
from django.utils.translation import gettext_lazy as _
import os
from django.core.exceptions import ValidationError


class OrganizationSetting(models.Model):
    """
    Singleton model to store organization-wide settings and branding information.
    Only one instance should exist.
    """

    INACTIVITY_CHOICES = [
        (0, _('Disabled')),
        (30, _('30 days')),
        (60, _('60 days')),
        (90, _('90 days')),
        (180, _('6 months')),
        (365, _('1 year')),
    ]

    system_name = models.CharField(
        max_length=255,
        verbose_name=_("System Name"),
        help_text=_("Name of the system displayed in the title and headers.")
    )
    organization = models.CharField(
        max_length=255,
        verbose_name=_("Organization Name"),
        help_text=_("Full legal name of the organization.")
    )
    copyright = models.CharField(
        max_length=255,
        verbose_name=_("Copyright Text"),
        help_text=_("Example: © 2025 University of Gondar. All rights reserved.")
    )
    contact_info = models.TextField(
        verbose_name=_("Contact Information"),
        help_text=_("General contact information or description.")
    )
    contact_number = models.CharField(
        max_length=20,
        validators=[RegexValidator(r'^\+?\d{9,15}$', message=_("Enter a valid phone number."))],
        verbose_name=_("Contact Number"),
        help_text=_("Phone number in international format. Example: +251912345678.")
    )
    support_email = models.EmailField(
        verbose_name=_("Support Email"),
        help_text=_("Main email address for support and inquiries.")
    )
    address = models.TextField(
        verbose_name=_("Physical Address"),
        help_text=_("Physical address of the organization (street, city, country).")
    )
    po_box = models.CharField(
        max_length=50,
        verbose_name=_("P.O. Box"),
        help_text=_("Postal box number if available."),
        blank=True,
        null=True
    )
    footer_logo = models.ImageField(
        upload_to='logos/footer/',
        verbose_name=_("Footer Logo"),
        blank=True,
        null=True,
        help_text=_("Logo displayed in the footer area.")
    )
    header_logo = models.ImageField(
        upload_to='logos/header/',
        verbose_name=_("Header Logo"),
        blank=True,
        null=True,
        help_text=_("Logo displayed in the header area.")
    )
    favicon = models.ImageField(
        upload_to='logos/favicon/',
        verbose_name=_("Favicon"),
        blank=True,
        null=True,
        help_text=_("Small icon displayed in browser tabs (recommended size: 32x32px).")
    )
    primary_color = models.CharField(
        max_length=7,
        verbose_name=_("Primary Color"),
        help_text=_("Primary brand color in hex format (e.g., #1a73c0)."),
        default="#1a73c0",
        validators=[
            RegexValidator(
                r'^#([A-Fa-f0-9]{6})$',
                message=_("Enter a valid hex color code (e.g., #1a73c0).")
            )
        ]
    )
    secondary_color = models.CharField(
        max_length=7,
        verbose_name=_("Secondary Color"),
        help_text=_("Secondary brand color in hex format (e.g., #f5f5f5)."),
        default="#f5f5f5",
        validators=[
            RegexValidator(
                r'^#([A-Fa-f0-9]{6})$',
                message=_("Enter a valid hex color code (e.g., #f5f5f5).")
            )
        ]
    )
    account_inactivity_period = models.IntegerField(
        choices=INACTIVITY_CHOICES,
        default=0,
        verbose_name=_("Account Inactivity Period"),
        help_text=_(
            "Automatically deactivate user accounts after this period of inactivity. "
            "Set to 'Disabled' to turn off this feature."
        )
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_("Last Updated")
    )

    class Meta:
        verbose_name = _("Organization Setting")
        verbose_name_plural = _("Organization Settings")

    def __str__(self):
        return self.system_name

    def clean(self):
        # Enforce singleton: only one instance allowed
        if OrganizationSetting.objects.exists() and not self.pk:
            raise ValidationError(_("Only one organization settings instance can exist."))

    def save(self, *args, **kwargs):
        self.full_clean()  # Calls clean() to enforce singleton before saving
        return super().save(*args, **kwargs)

    @classmethod
    def get_settings(cls):
        """
        Return the existing organization settings instance,
        or create default one if it doesn't exist.
        """
        settings, created = cls.objects.get_or_create(
            defaults={
                'system_name': 'Sample Application Portal',
                'organization': 'Sample Organization',
                'copyright': '© 2025 Sample Organization. All rights reserved.',
                'contact_info': 'Contact us for more information.',
                'contact_number': '+**********',
                'support_email': '<EMAIL>',
                'address': 'Sample Address',
                'po_box': 'P.O. Box 123',
                'primary_color': '#1a73c0',
                'secondary_color': '#2A2525',
                'account_inactivity_period': 0,
            }
        )
        return settings


class QuickLink(models.Model):
    """
    Quick links displayed in the footer or other designated areas.
    """

    name = models.CharField(
        max_length=255,
        verbose_name=_("Link Name"),
        help_text=_("Display name for the quick link.")
    )
    url = models.URLField(
        verbose_name=_("Link URL"),
        help_text=_("Full URL to link to (must start with http:// or https://).")
    )
    description = models.CharField(
        max_length=255,
        verbose_name=_("Description"),
        help_text=_("Brief description of the link (optional)."),
        blank=True,
        null=True
    )
    is_external = models.BooleanField(
        default=True,
        verbose_name=_("External Link"),
        help_text=_("If checked, link will open in a new tab.")
    )
    order = models.PositiveIntegerField(
        default=0,
        verbose_name=_("Order"),
        help_text=_("Order of appearance. Lower numbers appear first.")
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Active"),
        help_text=_("If unchecked, this link will not be displayed.")
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_("Created At")
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_("Last Updated")
    )

    class Meta:
        ordering = ['order', 'name']
        verbose_name = _("Quick Link")
        verbose_name_plural = _("Quick Links")

    def __str__(self):
        return self.name


class SocialMediaLink(models.Model):
    """
    Social media links for the organization.
    """

    PLATFORM_CHOICES = [
        ('facebook', _('Facebook')),
        ('twitter', _('Twitter')),
        ('linkedin', _('LinkedIn')),
        ('youtube', _('YouTube')),
        ('telegram', _('Telegram')),
        ('instagram', _('Instagram')),
        ('tiktok', _('TikTok')),
        ('other', _('Other')),
    ]

    platform = models.CharField(
        max_length=50,
        choices=PLATFORM_CHOICES,
        verbose_name=_("Platform"),
        help_text=_("Select the social media platform.")
    )
    url = models.URLField(
        verbose_name=_("Profile URL"),
        help_text=_("Full URL to the organization's profile/page.")
    )
    icon = models.ImageField(
        upload_to='social_icons/',
        verbose_name=_("Platform Icon"),
        blank=True,
        null=True,
        help_text=_("Custom icon for the platform. Optional.")
    )
    display_name = models.CharField(
        max_length=100,
        verbose_name=_("Display Name"),
        help_text=_("Custom display name (e.g., 'Follow us on Facebook')."),
        blank=True,
        null=True
    )
    order = models.PositiveIntegerField(
        default=0,
        verbose_name=_("Order"),
        help_text=_("Order of appearance. Lower numbers appear first.")
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Active"),
        help_text=_("If unchecked, this link will not be displayed.")
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_("Created At")
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_("Last Updated")
    )

    class Meta:
        ordering = ['order', 'platform']
        verbose_name = _("Social Media Link")
        verbose_name_plural = _("Social Media Links")
        constraints = [
            models.UniqueConstraint(fields=['platform'], name='unique_social_platform')
        ]

    def __str__(self):
        return self.display_name if self.display_name else self.get_platform_display()

    def get_icon_url(self):
        """
        Returns the URL for the platform icon if set,
        otherwise None. Could be extended to provide default icons.
        """
        if self.icon:
            return self.icon.url
        return None


# SMTP models are available in smtp_models.py
