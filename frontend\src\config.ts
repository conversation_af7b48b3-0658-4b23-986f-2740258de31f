// API Configuration
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';
export const API_TIMEOUT = parseInt(import.meta.env.VITE_API_TIMEOUT || '30000');

// Payment Gateway Configuration
export const PAYMENT_BASE_URL = import.meta.env.VITE_PAYMENT_BASE_URL || 'http://localhost:8000/api/payment';
export const MERCHANT_APP_ID = import.meta.env.VITE_MERCHANT_APP_ID || 'sample-merchant-id';

// Legacy Configuration (for backward compatibility)
export const LEGACY_BASE_URL = import.meta.env.VITE_LEGACY_BASE_URL || 'http://localhost:8000/api';
export const LEGACY_MERCHANT_APP_ID = import.meta.env.VITE_LEGACY_MERCHANT_APP_ID || 'sample-legacy-merchant-id';

// Application Configuration
export const APP_VERSION = import.meta.env.VITE_APP_VERSION || '1.0.0';

// Default Settings
export const DEFAULT_PAGINATION_LIMIT = parseInt(import.meta.env.VITE_DEFAULT_PAGINATION_LIMIT || '10');
export const DEFAULT_THEME = import.meta.env.VITE_DEFAULT_THEME || 'light';

// Feature Flags
export const FEATURES = {
  ENABLE_DARK_MODE: import.meta.env.VITE_ENABLE_DARK_MODE === 'true',
  ENABLE_NOTIFICATIONS: import.meta.env.VITE_ENABLE_NOTIFICATIONS === 'true',
};

// Backend Media URL
export const BACKEND_MEDIA_URL = import.meta.env.VITE_BACKEND_MEDIA_URL || 'http://localhost:8000';
